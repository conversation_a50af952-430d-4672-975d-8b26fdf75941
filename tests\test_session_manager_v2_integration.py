import asyncio
import pytest
import sys
import os
from unittest.mock import AsyncMock, MagicMock, patch

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from core.session_manager_v2 import SessionManagerV2
from agents.orchestrator_agent_v3 import OrchestratorV3


class TestSessionManagerV2Integration:
    """
    Integration tests for Session Manager v2 and Orchestrator v3.
    
    These tests verify:
    1. Complete session lifecycle management
    2. Proper component initialization and dependency injection
    3. Comprehensive cleanup and resource management
    4. System shutdown procedures
    """
    
    @pytest.fixture
    async def mock_redis_client(self):
        """Mock Redis client for testing."""
        mock_redis = AsyncMock()
        mock_redis.client = AsyncMock()
        mock_redis.client.scan = AsyncMock(return_value=(0, []))
        mock_redis.delete = AsyncMock()
        mock_redis.close = AsyncMock()
        mock_redis.get = AsyncMock(return_value=None)
        mock_redis.subscribe = AsyncMock()
        return mock_redis
    
    @pytest.fixture
    async def mock_state_manager(self):
        """Mock State Manager for testing."""
        mock_state_manager = AsyncMock()
        mock_state_manager.current_state_id = "stt_process"
        mock_state_manager.workflow_name = "test_workflow"
        mock_state_manager.current_pipeline_step_id = "stt_step"
        mock_state_manager.memory_manager = AsyncMock()
        mock_state_manager.end_session_cleanup = AsyncMock()
        mock_state_manager.execute_state = AsyncMock()
        mock_state_manager.transition_pipeline_state = AsyncMock()
        mock_state_manager.workflow = MagicMock()
        mock_state_manager.workflow.get_state = MagicMock(return_value=None)
        mock_state_manager.workflow.get_next_state = MagicMock(return_value=None)
        return mock_state_manager
    
    @pytest.fixture
    async def mock_memory_manager(self):
        """Mock Memory Manager for testing."""
        mock_memory_manager = AsyncMock()
        mock_memory_manager.set = AsyncMock()
        mock_memory_manager.get = AsyncMock(return_value="test query")
        mock_memory_manager.save_persistent_memory_data = AsyncMock()
        mock_memory_manager.clear_ephemeral = AsyncMock()
        return mock_memory_manager
    
    @pytest.fixture
    async def session_manager(self, mock_redis_client):
        """Create Session Manager v2 instance for testing."""
        with patch('core.session_manager_v2.RedisClient', return_value=mock_redis_client):
            session_manager = SessionManagerV2(redis_url="redis://localhost:6379")
            yield session_manager
            # Cleanup
            try:
                await session_manager.shutdown()
            except:
                pass
    
    @pytest.mark.asyncio
    async def test_session_creation_lifecycle(self, session_manager, mock_state_manager, mock_memory_manager):
        """Test complete session creation lifecycle."""
        
        # Mock StateManager.create to return our mock
        with patch('core.session_manager_v2.StateManager.create', return_value=mock_state_manager):
            # Mock the memory manager on the state manager
            mock_state_manager.memory_manager = mock_memory_manager
            
            # Create session
            session_id = await session_manager.create_session(
                workflow_name="test_workflow",
                user_id="test_user"
            )
            
            # Verify session was created
            assert session_id is not None
            assert session_id in session_manager.active_sessions
            
            # Verify session info
            session_info = session_manager.get_session_info(session_id)
            assert session_info is not None
            assert session_info["workflow_name"] == "test_workflow"
            assert session_info["user_id"] == "test_user"
            assert session_info["status"] == "active"
            
            # Verify memory manager was called to set metadata
            mock_memory_manager.set.assert_called()
    
    @pytest.mark.asyncio
    async def test_orchestrator_initialization(self, session_manager, mock_state_manager, mock_memory_manager):
        """Test Orchestrator v3 initialization with dependency injection."""
        
        with patch('core.session_manager_v2.StateManager.create', return_value=mock_state_manager):
            mock_state_manager.memory_manager = mock_memory_manager
            
            # Create session
            session_id = await session_manager.create_session(
                workflow_name="test_workflow",
                user_id="test_user"
            )
            
            # Mock OrchestratorV3 to avoid actual initialization
            with patch('agents.orchestrator_agent_v3.OrchestratorV3') as mock_orchestrator_class:
                mock_orchestrator = AsyncMock()
                mock_orchestrator_class.return_value = mock_orchestrator
                
                # Initialize orchestrator
                orchestrator = await session_manager.initialize_orchestrator(session_id)
                
                # Verify orchestrator was created with correct parameters
                mock_orchestrator_class.assert_called_once_with(
                    session_id=session_id,
                    workflow_name="test_workflow",
                    state_manager=mock_state_manager,
                    memory_manager=mock_memory_manager,
                    redis_client=session_manager.redis_client
                )
                
                # Verify orchestrator is stored in session
                session_info = session_manager.active_sessions[session_id]
                assert "orchestrator" in session_info
    
    @pytest.mark.asyncio
    async def test_dialog_log_saving(self, session_manager, mock_state_manager, mock_memory_manager):
        """Test dialog log saving functionality."""
        
        with patch('core.session_manager_v2.StateManager.create', return_value=mock_state_manager):
            mock_state_manager.memory_manager = mock_memory_manager
            
            # Create session
            session_id = await session_manager.create_session(
                workflow_name="test_workflow",
                user_id="test_user"
            )
            
            # Save dialog log
            result = await session_manager.save_dialog_log(session_id)
            
            # Verify success
            assert result is True
            
            # Verify memory manager was called
            mock_memory_manager.save_persistent_memory_data.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_session_cleanup(self, session_manager, mock_state_manager, mock_memory_manager):
        """Test comprehensive session cleanup."""
        
        with patch('core.session_manager_v2.StateManager.create', return_value=mock_state_manager):
            mock_state_manager.memory_manager = mock_memory_manager
            
            # Create session
            session_id = await session_manager.create_session(
                workflow_name="test_workflow",
                user_id="test_user"
            )
            
            # Add mock orchestrator
            mock_orchestrator = AsyncMock()
            session_manager.active_sessions[session_id]["orchestrator"] = mock_orchestrator
            
            # Cleanup session
            result = await session_manager.cleanup_session(session_id, "test_cleanup")
            
            # Verify cleanup was successful
            assert result is True
            
            # Verify session was removed
            assert session_id not in session_manager.active_sessions
            
            # Verify all cleanup methods were called
            mock_memory_manager.save_persistent_memory_data.assert_called()
            mock_memory_manager.clear_ephemeral.assert_called()
            mock_state_manager.end_session_cleanup.assert_called()
            mock_orchestrator.cleanup.assert_called()
    
    @pytest.mark.asyncio
    async def test_system_shutdown(self, session_manager, mock_state_manager, mock_memory_manager):
        """Test graceful system shutdown."""
        
        with patch('core.session_manager_v2.StateManager.create', return_value=mock_state_manager):
            mock_state_manager.memory_manager = mock_memory_manager
            
            # Create multiple sessions
            session_ids = []
            for i in range(3):
                session_id = await session_manager.create_session(
                    workflow_name=f"test_workflow_{i}",
                    user_id=f"test_user_{i}"
                )
                session_ids.append(session_id)
            
            # Verify sessions exist
            assert len(session_manager.active_sessions) == 3
            
            # Shutdown system
            await session_manager.shutdown()
            
            # Verify all sessions were cleaned up
            assert len(session_manager.active_sessions) == 0
            assert session_manager.is_running is False
    
    @pytest.mark.asyncio
    async def test_session_not_found_handling(self, session_manager):
        """Test handling of operations on non-existent sessions."""
        
        # Try to save dialog log for non-existent session
        result = await session_manager.save_dialog_log("non_existent_session")
        assert result is False
        
        # Try to cleanup non-existent session
        result = await session_manager.cleanup_session("non_existent_session", "test")
        assert result is False
        
        # Try to get info for non-existent session
        info = session_manager.get_session_info("non_existent_session")
        assert info is None
        
        # Try to initialize orchestrator for non-existent session
        with pytest.raises(ValueError):
            await session_manager.initialize_orchestrator("non_existent_session")


if __name__ == "__main__":
    # Run a simple test
    async def simple_test():
        print("Running simple Session Manager v2 integration test...")
        
        # Mock dependencies
        with patch('core.session_manager_v2.RedisClient') as mock_redis_class:
            mock_redis = AsyncMock()
            mock_redis.client = AsyncMock()
            mock_redis.client.scan = AsyncMock(return_value=(0, []))
            mock_redis.delete = AsyncMock()
            mock_redis.close = AsyncMock()
            mock_redis_class.return_value = mock_redis
            
            with patch('core.session_manager_v2.StateManager.create') as mock_state_create:
                mock_state_manager = AsyncMock()
                mock_state_manager.current_state_id = "stt_process"
                mock_state_manager.workflow_name = "test_workflow"
                mock_state_manager.memory_manager = AsyncMock()
                mock_state_manager.end_session_cleanup = AsyncMock()
                mock_state_create.return_value = mock_state_manager
                
                # Create session manager
                session_manager = SessionManagerV2()
                
                try:
                    # Test session creation
                    session_id = await session_manager.create_session("test_workflow", "test_user")
                    print(f"✓ Session created: {session_id}")
                    
                    # Test session info retrieval
                    info = session_manager.get_session_info(session_id)
                    print(f"✓ Session info retrieved: {info['workflow_name']}")
                    
                    # Test cleanup
                    result = await session_manager.cleanup_session(session_id, "test_complete")
                    print(f"✓ Session cleanup: {result}")
                    
                    # Test shutdown
                    await session_manager.shutdown()
                    print("✓ System shutdown completed")
                    
                    print("\nAll tests passed! ✓")
                    
                except Exception as e:
                    print(f"✗ Test failed: {e}")
                    import traceback
                    traceback.print_exc()
    
    asyncio.run(simple_test())
