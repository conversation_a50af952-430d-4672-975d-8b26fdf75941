"""
Test Session Manager v2 and Orchestrator v3 with real workflow files.

This test uses actual workflow files from the repository to demonstrate
the complete functionality with Redis connectivity.
"""

import asyncio
import sys
import os

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from core.session_manager_v2 import SessionManagerV2


async def test_with_real_workflow():
    """Test Session Manager v2 with a real workflow file."""
    
    print("🎯 Testing Session Manager v2 & Orchestrator v3 with Real Workflow\n")
    
    # Initialize session manager
    session_manager = SessionManagerV2()
    
    try:
        # Test with existing workflow file
        workflow_name = "GenericBank"  # This file exists in the repository
        user_id = "test_user_real_workflow"
        
        print(f"🚀 Creating session with workflow: {workflow_name}")
        
        # Create session
        session_id = await session_manager.create_session(workflow_name, user_id)
        print(f"✅ Session created successfully: {session_id}")
        
        # Get session info
        session_info = session_manager.get_session_info(session_id)
        print(f"📊 Session Info:")
        print(f"   - Workflow: {session_info['workflow_name']}")
        print(f"   - User ID: {session_info['user_id']}")
        print(f"   - Status: {session_info['status']}")
        print(f"   - Created: {session_info['created_at']}")
        
        # Initialize orchestrator
        print(f"\n🔧 Initializing Orchestrator v3...")
        orchestrator = await session_manager.initialize_orchestrator(session_id)
        print(f"✅ Orchestrator v3 initialized successfully")
        
        # Get orchestrator status
        orch_status = orchestrator.get_status()
        print(f"📊 Orchestrator Status:")
        print(f"   - Session ID: {orch_status['session_id']}")
        print(f"   - Workflow: {orch_status['workflow_name']}")
        print(f"   - Running: {orch_status['running']}")
        print(f"   - Current State: {orch_status['current_state']}")
        print(f"   - Pipeline Step: {orch_status['current_pipeline_step']}")
        
        # Save dialog log
        print(f"\n💾 Saving dialog log...")
        save_result = await session_manager.save_dialog_log(session_id)
        print(f"✅ Dialog log saved: {save_result}")
        
        # List active sessions
        active_sessions = session_manager.list_active_sessions()
        print(f"\n📋 Active sessions: {len(active_sessions)}")
        for session in active_sessions:
            print(f"   - {session}")
        
        # Test session cleanup
        print(f"\n🧹 Cleaning up session...")
        cleanup_result = await session_manager.cleanup_session(session_id, "test_complete")
        print(f"✅ Session cleanup completed: {cleanup_result}")
        
        # Verify session was removed
        remaining_sessions = session_manager.list_active_sessions()
        print(f"📋 Remaining active sessions: {len(remaining_sessions)}")
        
        print(f"\n🎉 All tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Always shutdown gracefully
        print(f"\n🔄 Shutting down system...")
        await session_manager.shutdown()
        print(f"✅ System shutdown completed")


async def test_multiple_workflows():
    """Test multiple concurrent sessions with different workflows."""
    
    print("\n" + "="*60)
    print("🎯 Testing Multiple Concurrent Sessions\n")
    
    session_manager = SessionManagerV2()
    
    try:
        # Available workflows
        workflows = ["GenericBank", "AgentsTry", "AgentsTry2"]
        sessions = []
        
        # Create multiple sessions
        print("🚀 Creating multiple sessions...")
        for i, workflow in enumerate(workflows):
            try:
                session_id = await session_manager.create_session(workflow, f"user_{i+1}")
                sessions.append(session_id)
                print(f"   ✅ Session {i+1}: {session_id} (workflow: {workflow})")
            except Exception as e:
                print(f"   ❌ Failed to create session for {workflow}: {e}")
        
        # List all active sessions
        active_sessions = session_manager.list_active_sessions()
        print(f"\n📊 Total active sessions: {len(active_sessions)}")
        
        # Initialize orchestrators for all sessions
        print(f"\n🔧 Initializing orchestrators...")
        orchestrators = []
        for session_id in sessions:
            try:
                orchestrator = await session_manager.initialize_orchestrator(session_id)
                orchestrators.append(orchestrator)
                print(f"   ✅ Orchestrator initialized for: {session_id}")
            except Exception as e:
                print(f"   ❌ Failed to initialize orchestrator for {session_id}: {e}")
        
        # Save dialog logs for all sessions
        print(f"\n💾 Saving dialog logs...")
        for session_id in sessions:
            try:
                result = await session_manager.save_dialog_log(session_id)
                print(f"   ✅ Dialog saved for {session_id}: {result}")
            except Exception as e:
                print(f"   ❌ Failed to save dialog for {session_id}: {e}")
        
        # Clean up all sessions
        print(f"\n🧹 Cleaning up all sessions...")
        for session_id in sessions:
            try:
                result = await session_manager.cleanup_session(session_id, "batch_cleanup")
                print(f"   ✅ Cleaned up {session_id}: {result}")
            except Exception as e:
                print(f"   ❌ Failed to cleanup {session_id}: {e}")
        
        # Verify all sessions are cleaned up
        remaining_sessions = session_manager.list_active_sessions()
        print(f"\n📊 Remaining sessions after cleanup: {len(remaining_sessions)}")
        
        print(f"\n🎉 Multiple sessions test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Multiple sessions test failed: {e}")
        return False
        
    finally:
        await session_manager.shutdown()


async def test_error_scenarios():
    """Test error handling scenarios."""
    
    print("\n" + "="*60)
    print("🎯 Testing Error Handling Scenarios\n")
    
    session_manager = SessionManagerV2()
    
    try:
        # Test 1: Invalid workflow
        print("🧪 Test 1: Invalid workflow name...")
        try:
            session_id = await session_manager.create_session("NonExistentWorkflow", "test_user")
            print(f"   ❌ Unexpected success: {session_id}")
        except Exception as e:
            print(f"   ✅ Expected error caught: {type(e).__name__}")
        
        # Test 2: Operations on non-existent session
        print(f"\n🧪 Test 2: Operations on non-existent session...")
        
        # Try to save dialog for non-existent session
        result = await session_manager.save_dialog_log("fake_session_id")
        print(f"   ✅ Save dialog result: {result}")
        
        # Try to cleanup non-existent session
        result = await session_manager.cleanup_session("fake_session_id", "test")
        print(f"   ✅ Cleanup result: {result}")
        
        # Try to get info for non-existent session
        info = session_manager.get_session_info("fake_session_id")
        print(f"   ✅ Session info result: {info}")
        
        # Try to initialize orchestrator for non-existent session
        try:
            await session_manager.initialize_orchestrator("fake_session_id")
            print(f"   ❌ Unexpected success")
        except ValueError as e:
            print(f"   ✅ Expected ValueError caught: {e}")
        
        print(f"\n🎉 Error handling tests completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
        return False
        
    finally:
        await session_manager.shutdown()


async def main():
    """Run all tests."""
    
    print("🚀 Session Manager v2 & Orchestrator v3 - Real Workflow Tests")
    print("="*60)
    
    results = []
    
    # Run tests
    results.append(await test_with_real_workflow())
    results.append(await test_multiple_workflows())
    results.append(await test_error_scenarios())
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print("\n" + "="*60)
    print(f"📊 Test Summary: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Session Manager v2 and Orchestrator v3 are working perfectly with Redis!")
    else:
        print("❌ Some tests failed. Please check the implementation.")
    
    return passed == total


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
