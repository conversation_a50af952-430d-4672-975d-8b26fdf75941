import asyncio
import sys
import os
from unittest.mock import AsyncMock, MagicMock, patch

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)


async def test_basic_session_creation():
    """Test basic session creation without complex dependencies."""
    
    print("Testing basic Session Manager v2 functionality...")
    
    # Mock all external dependencies
    with patch('core.session_manager_v2.RedisClient') as mock_redis_class:
        mock_redis = AsyncMock()
        mock_redis.client = AsyncMock()
        mock_redis.client.scan = AsyncMock(return_value=(0, []))
        mock_redis.delete = AsyncMock()
        mock_redis.close = AsyncMock()
        mock_redis_class.return_value = mock_redis
        
        with patch('core.session_manager_v2.StateManager.create') as mock_state_create:
            # Create mock state manager
            mock_state_manager = AsyncMock()
            mock_state_manager.current_state_id = "stt_process"
            mock_state_manager.workflow_name = "test_workflow"
            
            # Create mock memory manager
            mock_memory_manager = AsyncMock()
            mock_memory_manager.set = AsyncMock()
            mock_memory_manager.get = AsyncMock(return_value="test query")
            mock_memory_manager.save_persistent_memory_data = AsyncMock()
            mock_memory_manager.clear_ephemeral = AsyncMock()
            
            # Attach memory manager to state manager
            mock_state_manager.memory_manager = mock_memory_manager
            mock_state_manager.end_session_cleanup = AsyncMock()
            
            mock_state_create.return_value = mock_state_manager
            
            # Import and create session manager
            from core.session_manager_v2 import SessionManagerV2
            session_manager = SessionManagerV2()
            
            try:
                # Test 1: Session creation
                print("1. Testing session creation...")
                session_id = await session_manager.create_session("test_workflow", "test_user")
                print(f"   ✓ Session created: {session_id}")
                
                # Test 2: Session info retrieval
                print("2. Testing session info retrieval...")
                info = session_manager.get_session_info(session_id)
                assert info is not None
                assert info['workflow_name'] == "test_workflow"
                assert info['user_id'] == "test_user"
                print(f"   ✓ Session info retrieved: {info['workflow_name']}")
                
                # Test 3: List active sessions
                print("3. Testing active sessions list...")
                active_sessions = session_manager.list_active_sessions()
                assert session_id in active_sessions
                print(f"   ✓ Active sessions: {len(active_sessions)}")
                
                # Test 4: Dialog log saving
                print("4. Testing dialog log saving...")
                result = await session_manager.save_dialog_log(session_id)
                assert result is True
                print("   ✓ Dialog log saved successfully")
                
                # Test 5: Session cleanup
                print("5. Testing session cleanup...")
                result = await session_manager.cleanup_session(session_id, "test_complete")
                assert result is True
                assert session_id not in session_manager.active_sessions
                print("   ✓ Session cleanup completed")
                
                # Test 6: System shutdown
                print("6. Testing system shutdown...")
                await session_manager.shutdown()
                assert session_manager.is_running is False
                print("   ✓ System shutdown completed")
                
                print("\n🎉 All tests passed successfully!")
                return True
                
            except Exception as e:
                print(f"❌ Test failed: {e}")
                import traceback
                traceback.print_exc()
                return False


async def test_orchestrator_integration():
    """Test Orchestrator v3 integration."""
    
    print("\nTesting Orchestrator v3 integration...")
    
    # Mock dependencies
    with patch('core.session_manager_v2.RedisClient') as mock_redis_class:
        mock_redis = AsyncMock()
        mock_redis.client = AsyncMock()
        mock_redis.client.scan = AsyncMock(return_value=(0, []))
        mock_redis.delete = AsyncMock()
        mock_redis.close = AsyncMock()
        mock_redis.subscribe = AsyncMock()
        mock_redis_class.return_value = mock_redis
        
        with patch('core.session_manager_v2.StateManager.create') as mock_state_create:
            mock_state_manager = AsyncMock()
            mock_state_manager.current_state_id = "stt_process"
            mock_state_manager.workflow_name = "test_workflow"
            mock_state_manager.memory_manager = AsyncMock()
            mock_state_manager.end_session_cleanup = AsyncMock()
            mock_state_create.return_value = mock_state_manager
            
            # Import session manager
            from core.session_manager_v2 import SessionManagerV2
            session_manager = SessionManagerV2()
            
            try:
                # Create session
                session_id = await session_manager.create_session("test_workflow", "test_user")
                print(f"   ✓ Session created for orchestrator test: {session_id}")
                
                # Mock OrchestratorV3
                with patch('agents.orchestrator_agent_v3.OrchestratorV3') as mock_orchestrator_class:
                    mock_orchestrator = AsyncMock()
                    mock_orchestrator.cleanup = AsyncMock()
                    mock_orchestrator_class.return_value = mock_orchestrator
                    
                    # Test orchestrator initialization
                    orchestrator = await session_manager.initialize_orchestrator(session_id)
                    print("   ✓ Orchestrator initialized with dependency injection")
                    
                    # Verify orchestrator was created with correct parameters
                    mock_orchestrator_class.assert_called_once()
                    call_args = mock_orchestrator_class.call_args
                    assert call_args.kwargs['session_id'] == session_id
                    assert call_args.kwargs['workflow_name'] == "test_workflow"
                    print("   ✓ Orchestrator created with correct parameters")
                    
                    # Test cleanup with orchestrator
                    result = await session_manager.cleanup_session(session_id, "test_complete")
                    assert result is True
                    mock_orchestrator.cleanup.assert_called_once()
                    print("   ✓ Orchestrator cleanup called during session cleanup")
                
                await session_manager.shutdown()
                print("   ✓ Orchestrator integration test completed")
                return True
                
            except Exception as e:
                print(f"❌ Orchestrator integration test failed: {e}")
                import traceback
                traceback.print_exc()
                return False


async def test_error_handling():
    """Test error handling scenarios."""
    
    print("\nTesting error handling...")
    
    with patch('core.session_manager_v2.RedisClient') as mock_redis_class:
        mock_redis = AsyncMock()
        mock_redis.client = AsyncMock()
        mock_redis.client.scan = AsyncMock(return_value=(0, []))
        mock_redis.delete = AsyncMock()
        mock_redis.close = AsyncMock()
        mock_redis_class.return_value = mock_redis
        
        from core.session_manager_v2 import SessionManagerV2
        session_manager = SessionManagerV2()
        
        try:
            # Test operations on non-existent session
            result = await session_manager.save_dialog_log("non_existent")
            assert result is False
            print("   ✓ Non-existent session dialog log handling")
            
            result = await session_manager.cleanup_session("non_existent", "test")
            assert result is False
            print("   ✓ Non-existent session cleanup handling")
            
            info = session_manager.get_session_info("non_existent")
            assert info is None
            print("   ✓ Non-existent session info handling")
            
            # Test orchestrator initialization on non-existent session
            try:
                await session_manager.initialize_orchestrator("non_existent")
                assert False, "Should have raised ValueError"
            except ValueError:
                print("   ✓ Non-existent session orchestrator initialization handling")
            
            await session_manager.shutdown()
            print("   ✓ Error handling tests completed")
            return True
            
        except Exception as e:
            print(f"❌ Error handling test failed: {e}")
            return False


async def main():
    """Run all tests."""
    print("🚀 Starting Session Manager v2 and Orchestrator v3 Tests\n")
    
    test_results = []
    
    # Run tests
    test_results.append(await test_basic_session_creation())
    test_results.append(await test_orchestrator_integration())
    test_results.append(await test_error_handling())
    
    # Summary
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"\n📊 Test Summary: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Session Manager v2 and Orchestrator v3 are working correctly.")
    else:
        print("❌ Some tests failed. Please check the implementation.")
    
    return passed == total


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
