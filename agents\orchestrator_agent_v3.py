import asyncio
import json
import os
import sys
from typing import Dict, Any, Optional
from utils.redis_client import RedisClient
from core.state_mgmt.StateManager import StateManager
from core.state_mgmt.memory_manager import MemoryManager
from schemas.a2a_message import A2AMessage, MessageType
from pydantic import ValidationError
from dotenv import load_dotenv
import openai

from core.logger_config import get_module_logger

load_dotenv()


class OrchestratorV3:
    """
    Orchestrator Agent V3 - Enhanced orchestrator with dependency injection and clear ownership.
    
    Key differences from V2:
    1. Accepts session ID as required parameter during creation/initialization
    2. Accepts workflow name as parameter when starting/running
    3. Retrieves user query from contextual memory via Memory Manager
    4. Retrieves agent responses from logger system
    5. Retrieves agent confidence scores from Redis publish messages
    6. Retrieves workflow/pipeline states from State Manager
    7. Makes OpenAI aware of states during decision-making
    8. Retrieves prohibited actions from State Manager
    9. Removes retry count tracking
    10. Moves save dialog log function to Session Manager v2
    11. Uses transition logic for agent-to-agent transitions
    12. Invokes transition pipeline state function for agent transitions
    13. Performs evaluation and transition after each pipeline state completion
    """
    
    def __init__(self, session_id: str, workflow_name: str, state_manager: StateManager, 
                 memory_manager: MemoryManager, redis_client: RedisClient):
        """
        Initialize Orchestrator v3 with dependency injection.
        
        Args:
            session_id: Required session identifier
            workflow_name: Workflow name for this orchestrator instance
            state_manager: Injected State Manager instance
            memory_manager: Injected Memory Manager instance
            redis_client: Injected Redis client instance
        """
        self.session_id = session_id
        self.workflow_name = workflow_name
        self.state_manager = state_manager
        self.memory_manager = memory_manager
        self.redis = redis_client
        
        # Initialize logger
        self.logger = get_module_logger("orchestrator_v3", session_id=session_id)
        
        # Orchestrator state
        self.running = False
        self._listen_task = None
        self.schema_version = "1.0"
        
        # OpenAI configuration
        self.openai_client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        
        self.logger.info(
            "OrchestratorV3 initialized with dependency injection",
            action="initialize",
            input_data={
                "session_id": session_id,
                "workflow_name": workflow_name,
                "state_manager_id": id(state_manager),
                "memory_manager_id": id(memory_manager)
            },
            status="success",
            layer="orchestrator_v3",
            step="init"
        )
    
    async def start(self, workflow_name: Optional[str] = None):
        """
        Start the orchestrator with optional workflow name override.
        
        Args:
            workflow_name: Optional workflow name to override the initialized one
        """
        if workflow_name:
            self.workflow_name = workflow_name
            
        try:
            self.running = True
            
            self.logger.info(
                "Starting OrchestratorV3",
                action="start",
                input_data={"session_id": self.session_id, "workflow_name": self.workflow_name},
                layer="orchestrator_v3",
                step="start_orchestrator"
            )
            
            # Start listening for agent completions
            self._listen_task = asyncio.create_task(
                self.redis.subscribe("agent_completion", self.handle_agent_completion)
            )
            
            # Execute the first state to start the workflow
            await self._start_workflow_execution()
            
            # Wait for the listen task
            await self._listen_task
            
        except Exception as e:
            self.logger.error(
                "Failed to start orchestrator",
                action="start",
                input_data={"session_id": self.session_id, "workflow_name": self.workflow_name},
                reason=str(e),
                status="error",
                layer="orchestrator_v3",
                step="start_failed"
            )
            raise
    
    async def _start_workflow_execution(self):
        """
        Start workflow execution by executing the first state.
        """
        try:
            # Get the first state from the workflow
            first_state_id = self.state_manager.current_state_id
            
            self.logger.info(
                "Starting workflow execution",
                action="start_workflow_execution",
                input_data={
                    "session_id": self.session_id,
                    "workflow_name": self.workflow_name,
                    "first_state_id": first_state_id
                },
                layer="orchestrator_v3",
                step="workflow_start"
            )
            
            # Execute the first state via state manager
            await self.state_manager.execute_state(first_state_id)
            
        except Exception as e:
            self.logger.error(
                "Failed to start workflow execution",
                action="start_workflow_execution",
                input_data={"session_id": self.session_id, "first_state_id": first_state_id},
                reason=str(e),
                layer="orchestrator_v3",
                step="workflow_start_failed"
            )
            raise
    
    async def handle_agent_completion(self, message: str):
        """
        Handle agent completion messages and orchestrate the workflow.
        
        Args:
            message: JSON string containing agent completion data
        """
        try:
            # Parse the message
            try:
                message_data = json.loads(message)
                a2a_message = A2AMessage(**message_data)
            except (json.JSONDecodeError, ValidationError) as e:
                self.logger.error(
                    "Invalid agent completion message format",
                    action="handle_agent_completion",
                    reason=str(e),
                    layer="orchestrator_v3",
                    step="message_parse_failed"
                )
                return
            
            # Check if this message is for our session
            if a2a_message.session_id != self.session_id:
                return  # Not for us
            
            self.logger.info(
                "Processing agent completion",
                action="handle_agent_completion",
                input_data={
                    "session_id": a2a_message.session_id,
                    "agent_name": a2a_message.agent_name,
                    "message_type": a2a_message.message_type.value
                },
                layer="orchestrator_v3",
                step="processing_completion"
            )
            
            # Retrieve data for decision making
            decision_context = await self._gather_decision_context(a2a_message)
            
            # Make decision using OpenAI with full context
            decision = await self._make_transition_decision(a2a_message, decision_context)
            
            # Execute the decision
            await self._execute_transition_decision(decision, a2a_message)
            
        except Exception as e:
            self.logger.error(
                "Error handling agent completion",
                action="handle_agent_completion",
                reason=str(e),
                layer="orchestrator_v3",
                step="completion_handling_failed"
            )
    
    async def _gather_decision_context(self, a2a_message: A2AMessage) -> Dict[str, Any]:
        """
        Gather all necessary context for decision making.
        
        Args:
            a2a_message: Agent completion message
            
        Returns:
            Dict containing all decision context
        """
        context = {}
        
        try:
            # Retrieve user query from contextual memory (clean text from preprocessing)
            user_query = await self.memory_manager.get("clean_text")
            context["user_query"] = user_query
            
            # Retrieve workflow states (layer1) from State Manager
            context["workflow_state"] = {
                "current_state_id": self.state_manager.current_state_id,
                "workflow_name": self.state_manager.workflow_name
            }
            
            # Retrieve pipeline states (layer2) from State Manager
            context["pipeline_state"] = {
                "current_pipeline_step": self.state_manager.current_pipeline_step_id
            }
            
            # Retrieve prohibited actions from State Manager
            context["prohibited_actions"] = await self._get_prohibited_actions()
            
            # Retrieve agent confidence scores from Redis
            confidence_key = f"session:{self.session_id}:confidence:{a2a_message.agent_name}"
            confidence_score = await self.redis.get(confidence_key)
            context["agent_confidence"] = confidence_score
            
            # Get agent response data
            context["agent_response"] = a2a_message.payload
            
            self.logger.debug(
                "Decision context gathered",
                action="gather_decision_context",
                input_data={"agent_name": a2a_message.agent_name},
                output_data={"context_keys": list(context.keys())},
                layer="orchestrator_v3",
                step="context_gathered"
            )
            
            return context
            
        except Exception as e:
            self.logger.error(
                "Failed to gather decision context",
                action="gather_decision_context",
                reason=str(e),
                layer="orchestrator_v3",
                step="context_gathering_failed"
            )
            raise

    async def _get_prohibited_actions(self) -> list:
        """
        Retrieve prohibited actions from State Manager.

        Returns:
            List of prohibited actions
        """
        try:
            # Get prohibited actions from state manager's workflow configuration
            prohibited_actions = []

            # Check if current state has prohibited actions
            current_state = self.state_manager.workflow.get_state(self.state_manager.current_state_id)
            if current_state and hasattr(current_state, 'prohibited_actions'):
                prohibited_actions = current_state.prohibited_actions

            return prohibited_actions

        except Exception as e:
            self.logger.warning(
                "Failed to retrieve prohibited actions",
                action="get_prohibited_actions",
                reason=str(e),
                layer="orchestrator_v3",
                step="prohibited_actions_failed"
            )
            return []

    async def _make_transition_decision(self, a2a_message: A2AMessage, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Make transition decision using OpenAI with full context awareness.

        Args:
            a2a_message: Agent completion message
            context: Decision context

        Returns:
            Dict containing decision information
        """
        try:
            # Prepare prompt with full context
            prompt = self._build_decision_prompt(a2a_message, context)

            # Call OpenAI for decision
            response = await asyncio.to_thread(
                self.openai_client.chat.completions.create,
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are an AI workflow orchestrator. Analyze the agent output and context to decide whether to proceed to the next agent or redo the current agent."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=500
            )

            decision_text = response.choices[0].message.content

            # Parse decision (simple implementation - can be enhanced)
            decision = {
                "action": "proceed" if "proceed" in decision_text.lower() else "redo",
                "reasoning": decision_text,
                "confidence": context.get("agent_confidence", 0.5),
                "next_agent": self._determine_next_agent() if "proceed" in decision_text.lower() else a2a_message.agent_name
            }

            self.logger.info(
                "Transition decision made",
                action="make_transition_decision",
                input_data={"agent_name": a2a_message.agent_name},
                output_data={"decision": decision["action"], "next_agent": decision["next_agent"]},
                layer="orchestrator_v3",
                step="decision_made"
            )

            return decision

        except Exception as e:
            self.logger.error(
                "Failed to make transition decision",
                action="make_transition_decision",
                reason=str(e),
                layer="orchestrator_v3",
                step="decision_failed"
            )
            # Default to proceed on error
            return {
                "action": "proceed",
                "reasoning": f"Error in decision making: {str(e)}",
                "confidence": 0.0,
                "next_agent": self._determine_next_agent()
            }

    def _build_decision_prompt(self, a2a_message: A2AMessage, context: Dict[str, Any]) -> str:
        """
        Build decision prompt with full context.

        Args:
            a2a_message: Agent completion message
            context: Decision context

        Returns:
            Formatted prompt string
        """
        prompt = f"""
        Analyze the following agent completion and decide whether to proceed or redo:

        Agent: {a2a_message.agent_name}
        Agent Output: {context.get('agent_response', 'No response')}
        Agent Confidence: {context.get('agent_confidence', 'Unknown')}

        User Query: {context.get('user_query', 'No query available')}

        Current Workflow State: {context.get('workflow_state', {})}
        Current Pipeline State: {context.get('pipeline_state', {})}

        Prohibited Actions: {context.get('prohibited_actions', [])}

        Based on this information, should we:
        1. PROCEED to the next agent in the pipeline
        2. REDO the current agent due to poor quality/confidence

        Respond with either "PROCEED" or "REDO" followed by your reasoning.
        """

        return prompt

    def _determine_next_agent(self) -> str:
        """
        Determine the next agent in the pipeline.

        Returns:
            Next agent name
        """
        try:
            # Get next state from state manager
            current_state = self.state_manager.current_state_id
            next_state = self.state_manager.workflow.get_next_state(current_state)

            if next_state:
                return next_state.id
            else:
                return "workflow_complete"

        except Exception as e:
            self.logger.warning(
                "Failed to determine next agent",
                action="determine_next_agent",
                reason=str(e),
                layer="orchestrator_v3",
                step="next_agent_failed"
            )
            return "workflow_complete"

    async def _execute_transition_decision(self, decision: Dict[str, Any], a2a_message: A2AMessage):
        """
        Execute the transition decision.

        Args:
            decision: Decision information
            a2a_message: Original agent completion message
        """
        try:
            if decision["action"] == "proceed":
                await self._proceed_to_next_agent(decision["next_agent"])
            else:
                await self._redo_current_agent(a2a_message.agent_name)

        except Exception as e:
            self.logger.error(
                "Failed to execute transition decision",
                action="execute_transition_decision",
                input_data={"decision": decision["action"], "agent": a2a_message.agent_name},
                reason=str(e),
                layer="orchestrator_v3",
                step="transition_execution_failed"
            )
            raise

    async def _proceed_to_next_agent(self, next_agent: str):
        """
        Proceed to the next agent using state manager transition.

        Args:
            next_agent: Next agent/state to transition to
        """
        try:
            if next_agent == "workflow_complete":
                self.logger.info(
                    "Workflow completed",
                    action="proceed_to_next_agent",
                    input_data={"session_id": self.session_id},
                    layer="orchestrator_v3",
                    step="workflow_complete"
                )
                self.running = False
                return

            # Use state manager to transition to next pipeline state
            await self.state_manager.transition_pipeline_state(next_agent)

            self.logger.info(
                "Transitioned to next agent",
                action="proceed_to_next_agent",
                input_data={"next_agent": next_agent, "session_id": self.session_id},
                layer="orchestrator_v3",
                step="agent_transition"
            )

        except Exception as e:
            self.logger.error(
                "Failed to proceed to next agent",
                action="proceed_to_next_agent",
                input_data={"next_agent": next_agent},
                reason=str(e),
                layer="orchestrator_v3",
                step="next_agent_failed"
            )
            raise

    async def _redo_current_agent(self, agent_name: str):
        """
        Redo the current agent by re-executing the current state.

        Args:
            agent_name: Agent to redo
        """
        try:
            # Re-execute current state
            current_state_id = self.state_manager.current_state_id
            await self.state_manager.execute_state(current_state_id)

            self.logger.info(
                "Re-executing current agent",
                action="redo_current_agent",
                input_data={"agent_name": agent_name, "state_id": current_state_id},
                layer="orchestrator_v3",
                step="agent_redo"
            )

        except Exception as e:
            self.logger.error(
                "Failed to redo current agent",
                action="redo_current_agent",
                input_data={"agent_name": agent_name},
                reason=str(e),
                layer="orchestrator_v3",
                step="agent_redo_failed"
            )
            raise

    async def cleanup(self):
        """
        Clean up orchestrator resources.
        """
        try:
            self.running = False

            # Cancel listen task
            if self._listen_task and not self._listen_task.done():
                self._listen_task.cancel()
                try:
                    await self._listen_task
                except asyncio.CancelledError:
                    pass

            self.logger.info(
                "OrchestratorV3 cleanup completed",
                action="cleanup",
                input_data={"session_id": self.session_id},
                layer="orchestrator_v3",
                step="cleanup_complete"
            )

        except Exception as e:
            self.logger.error(
                "Error during orchestrator cleanup",
                action="cleanup",
                reason=str(e),
                layer="orchestrator_v3",
                step="cleanup_failed"
            )

    async def close(self):
        """
        Close the orchestrator (alias for cleanup for compatibility).
        """
        await self.cleanup()

    def get_status(self) -> Dict[str, Any]:
        """
        Get orchestrator status information.

        Returns:
            Dict containing status information
        """
        return {
            "session_id": self.session_id,
            "workflow_name": self.workflow_name,
            "running": self.running,
            "current_state": self.state_manager.current_state_id if self.state_manager else None,
            "current_pipeline_step": self.state_manager.current_pipeline_step_id if self.state_manager else None
        }
